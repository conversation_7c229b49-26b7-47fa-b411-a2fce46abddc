body {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  font-family: 'Lexend Exa', sans-serif;
  padding: 0;
  margin: 0;
  min-height: 100vh;
  padding-top: 120px !important; /* Space for fixed navbar */
}

a {
  color: #4fc3f7;
  text-decoration: none;
}

a:hover {
  color: #29b6f6;
  text-shadow: 0px 0px 2px #81d4fa;
}

button:not(:last-child) {
  margin-right: 40px;
}
button {
  border: 2px solid transparent;
  border-radius: 5px;
  padding: 10px;
  transition: background 0.7s;
  color: #4fc3f7;
  text-decoration: none;
  text-transform: uppercase;
  cursor: pointer;
  margin-top: 30px;
}
button:hover {
  background: #1976d2;
  border: 2px solid #42a5f5;
  border-right: 2px solid #1565c0;
  border-bottom: 2px solid #1565c0;
  color: #f9f8fd;
}

.content {
  background-color: rgba(255, 255, 255, 0.1);
  margin: 0 auto;
  width: 65%;
  padding: 20px 60px;
  box-shadow: 0px 0px 35px -20px #1976d2;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.content p {
  text-align: center;
  margin: 30px 0px;
  white-space: pre-line;
  color: #fff;
}

.like_button {
  background-color: white;
  border: 2px solid transparent;
  border-radius: 5px;
  padding: 10px;
  transition: background 0.7s;
  color: #4fc3f7;
  text-decoration: none;
  text-transform: uppercase;
  cursor: pointer;
}

.like_button:hover {
  background: #1976d2;
  border: 2px solid #42a5f5;
  border-right: 2px solid #1565c0;
  border-bottom: 2px solid #1565c0;
  color: #f9f8fd;
}

.title {
  text-align: center;
  font-family: 'Times New Roman', Times, serif;
  font-size: 60px;
  font-weight: bold;
  text-decoration: 2px underline;
  text-decoration-color: rgba(25, 118, 210, 0.5);
  color: #fff;
}
/* ======================================== Home Page ============================================== */

.home_logo {
  display: block;
  margin: 0 auto;
  margin-bottom: 70px;
}

.site_title {
  text-align: center;
  font-size: 70px;
}

/* New Home Page Styles */
.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.hero-section {
  text-align: center;
  margin-bottom: 50px;
  padding: 40px 20px;
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.3) 0%, rgba(21, 101, 192, 0.4) 100%);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-section h1 {
  font-size: 3.5rem;
  color: #fff;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-subtitle {
  font-size: 1.3rem;
  color: #e3f2fd;
  margin-bottom: 30px;
  font-weight: 300;
}

.user-welcome {
  margin-top: 30px;
}

.user-welcome h2 {
  color: #fff;
  font-size: 2rem;
  margin-bottom: 25px;
}

.customer-actions, .company-actions, .guest-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.btn {
  display: inline-block;
  padding: 12px 25px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: bold;
  text-transform: uppercase;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  cursor: pointer;
}

.btn-primary {
  background: #1976d2;
  color: white;
  border-color: #1976d2;
}

.btn-primary:hover {
  background: #1565c0;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(25, 118, 210, 0.4);
}

.btn-secondary {
  background: transparent;
  color: #4fc3f7;
  border-color: #1976d2;
}

.btn-secondary:hover {
  background: #1976d2;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
}

.features-section {
  margin-top: 50px;
  padding: 30px;
  background: rgba(255,255,255,0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.features-section h3 {
  text-align: center;
  font-size: 2.5rem;
  color: #fff;
  margin-bottom: 30px;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.service-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.category {
  background: rgba(255,255,255,0.1);
  padding: 25px;
  border-radius: 10px;
  border: 1px solid rgba(255,255,255,0.2);
  transition: transform 0.3s ease;
  backdrop-filter: blur(5px);
}

.category:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(25, 118, 210, 0.3);
  border-color: rgba(79, 195, 247, 0.5);
}

.category h4 {
  color: #4fc3f7;
  font-size: 1.5rem;
  margin-bottom: 15px;
  text-align: center;
}

.category ul {
  list-style: none;
  padding: 0;
}

.category li {
  margin: 10px 0;
  text-align: center;
}

.category li a {
  color: #fff;
  text-decoration: none;
  padding: 8px 15px;
  border-radius: 5px;
  display: inline-block;
  transition: all 0.3s ease;
}

.category li a:hover {
  background: #1976d2;
  color: white;
  transform: scale(1.05);
}

/* ============================================================================================== */

/* ======================================== Navbar ============================================== */

.navbar {
  position: fixed !important;
  top: 15px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  box-shadow: 0 4px 20px rgba(25, 118, 210, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 30px;
  animation: slide-in 1s ease-out;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  width: 98%;
  max-width: 1400px;
  z-index: 1000 !important;
}

.navbar ul {
  position: relative;
  display: flex;
  flex: 1 1 auto;
  margin: 0;
  padding: 0 20px;
  list-style-type: none;
}
.navbar ul li:not(:last-child) {
  margin-right: 25px;
}
.navbar ul li {
  border: 2px solid transparent;
  border-radius: 5px;
  padding: 8px 12px;
  transition: background 0.2s;
}
.navbar ul li a {
  color: #4fc3f7;
  text-decoration: none;
  text-transform: uppercase;
  transition: color 0.2s;
  font-weight: 600;
}
.navbar ul li ul {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  display: block;
  margin: 12px -12px;
  padding: 0;
  background: rgba(25, 118, 210, 0.95);
  border: 2px solid #42a5f5;
  border-right: 2px solid #1565c0;
  border-bottom: 2px solid #1565c0;
  border-radius: 5px;
  transition: opacity 0.2s, visibility 0.2s;
  backdrop-filter: blur(10px);
  z-index: 1000;
  min-width: 200px;
}
.navbar ul li ul li {
  margin: 0;
  width: 100%;
  line-height: 1.7;
  padding: 8px 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar ul li ul li:last-child {
  border-bottom: none;
}

.navbar ul li ul li:hover {
  background: rgba(255, 255, 255, 0.1);
}

.navbar ul li:hover {
  background: #1976d2;
  border: 2px solid #42a5f5;
  border-right: 2px solid #1565c0;
  border-bottom: 2px solid #1565c0;
}
.navbar ul li:hover a {
  color: #f9f8fd;
}
.navbar ul li:hover ul {
  visibility: visible;
  opacity: 1;
  box-shadow: 0px 3px 15px rgba(25, 118, 210, 0.4);
}
.navbar ul li:hover ul li a {
  color: #f9f8fd;
}

.last_navbar {
  right: 0;
  position: absolute;
  margin-right: 20px;
}

/* Responsive Navigation */
@media (max-width: 1200px) {
  .navbar {
    width: 99%;
  }

  .navbar ul li:not(:last-child) {
    margin-right: 15px;
  }

  .navbar ul li {
    padding: 6px 8px;
  }

  .navbar ul li a {
    font-size: 0.9rem;
  }
}

@media (max-width: 900px) {
  .navbar ul li:not(:last-child) {
    margin-right: 10px;
  }

  .navbar ul li {
    padding: 5px 6px;
  }

  .navbar ul li a {
    font-size: 0.8rem;
  }
}

/* ============================================================================================== */

/* ======================================== Service Info ======================================== */

.service_info_header {
  display: block ruby;
}
.service_info_header h5 {
  float: right;
}

/* ============================================================================================== */

/* ======================================== Service List ======================================== */

.services_list {
  padding: 0;
  margin-bottom: 20px;
}

pre {
  display: none;
}

.service_list_info {
  padding: 0 30px;
}
.service_list_info li {
  list-style-type: none;
}
.service_list_info li a:hover > pre {
  display: block;
}

.line {
  width: 100%;
  margin: 0 auto;
  height: 2px;
  background-color: #00000044;
  border-radius: 20px;
  margin-bottom: 10px;
  margin-top: 35px;
}

.create_service {
  background: antiquewhite;
  padding: 10px 20px;
  border-radius: 17px;
  display: table;
  margin: 0 auto;
  border: 2px solid #474747;
  margin-bottom: 60px;
  text-decoration: none;
}

.create_service:hover {
  background-color: #b8b8b8;
  box-shadow: 2px 3px 6px 1px #21212173;
  transition: 0.5s;
}
.create_service:not(hover) {
  transition: 0.6s;
}

.list_services_profile {
  font-size: x-large;
}

/* ============================================================================================== */

/* ===================================== Choose user type ======================================= */

.choice_text {
  text-shadow: 0px 0px 1px #1565c0;
  color: white;
  text-align: center;
  font-size: 40px;
  font-weight: initial;
}

.choice {
  display: inline-grid;
}

.img {
  border: 1px solid #b8b8b8;
  border-radius: 14px;
  display: inline-block;
  padding: 20px;
  box-shadow: 0px 0px 5px 5px rgb(50, 50, 50);
  cursor: pointer;
}
.img:hover {
  box-shadow: 0px 0px 9px 5px rgb(27, 27, 27);
}
.img:active {
  background-color: #474747;
  transition: 0.1s;
  box-shadow: 0px 0px 9px 5px black;
}

.label_images {
  display: block;
}

/* ============================================================================================== */

/* =========================================== Forms ============================================ */

form label {
  display: none;
}

form {
  margin: 0 auto;
  display: grid;
  width: 65%;
}

/* form ul {
  display: none;
} */

form input {
  padding: 10px 3px;
  font-size: 20px;
  display: block;
  border: none;
  border-bottom: 3px solid #1976d2;
  background-color: inherit;
  margin-top: 30px;
  color: #fff;
}

form input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

form input:hover,
input:focus {
  transition: 0.5s;
  border-bottom: 3px solid #4fc3f7;
}

form select {
  display: block;
  font-size: 20px;
  padding: 6px 3px;
  background: inherit;
  border: none;
  border-bottom: 3px solid #1976d2;
  margin-top: 30px;
  color: #fff;
}

form select:focus,
select:hover {
  transition: 0.5s;
  border-bottom: 3px solid #4fc3f7;
}

.error_message {
  color: #ff6b6b;
  text-shadow: 0px 0px 10px #f44336;
}

form textarea {
  display: block;
  border: none;
  border-bottom: 3px solid black;
  background: inherit;
  font-size: inherit;
  font-weight: inherit;
  font-family: inherit;
  margin-top: 30px;
}

form textarea:hover,
textarea:focus {
  transition: 0.5s;
  border-bottom: 3px solid #4fc3f7;
}

span {
  background-color: #3834343d;
  border-radius: 30px;
  box-shadow: 0px 0px 23px 1px #2423213d;
  padding: 5px 10px;
  margin: 5px 0px;
}

/* ======================================== Service Cards ======================================== */

.service-card {
  background: rgba(255,255,255,0.1);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid rgba(255,255,255,0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.service-card h3, .service-card h4 {
  color: #4fc3f7;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.service-card h3 a, .service-card h4 a {
  color: #4fc3f7;
  text-decoration: none;
}

.service-card h3 a:hover, .service-card h4 a:hover {
  color: #29b6f6;
}

.service-card p {
  color: #fff;
  margin: 8px 0;
  line-height: 1.5;
}

.service-card p strong {
  color: #e3f2fd;
}

/* Request Cards for Customer Profile */
.request-card {
  background: rgba(255,255,255,0.1);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid rgba(255,255,255,0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.request-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.request-card h4 {
  color: #4fc3f7;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.request-details p {
  color: #fff;
  margin: 8px 0;
  line-height: 1.4;
}

.request-details strong {
  color: #f0f0f0;
}

.requests-list, .services-list {
  display: grid;
  gap: 20px;
}

.no-requests, .no-services {
  text-align: center;
  color: #ccc;
  font-style: italic;
  padding: 40px 20px;
  background: rgba(255,255,255,0.05);
  border-radius: 10px;
  margin: 20px 0;
}

.service-actions {
  margin-top: 15px;
  text-align: right;
}

/* ======================================== Profile Pages ======================================== */

.profile-container, .login-container, .request-service-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}



.profile-header, .service-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255,255,255,0.1);
  border-radius: 10px;
}

.profile-header h1, .service-header h1 {
  color: #fff;
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.profile-type, .service-category {
  color: #4fc3f7;
  font-size: 1.2rem;
  font-weight: bold;
}

.info-grid, .detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin: 20px 0;
}

.info-item, .detail-item {
  background: rgba(255,255,255,0.1);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid rgba(255,255,255,0.2);
}

.info-item label, .detail-item label {
  color: #f0f0f0;
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
}

.info-item span, .detail-item span {
  color: #fff;
  background: none;
  box-shadow: none;
  padding: 0;
  margin: 0;
}

/* ======================================== Form Improvements ======================================== */

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block !important;
  color: #f0f0f0;
  font-weight: bold;
  margin-bottom: 8px;
}

.error {
  color: #ff6b6b;
  font-size: 0.9rem;
  margin-top: 5px;
  display: block;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
}

.help-text {
  color: #ccc;
  font-size: 0.9rem;
  margin-top: 5px;
  display: block;
}

/* ======================================== Error Pages ======================================== */

.error-container {
  text-align: center;
  padding: 50px 20px;
}

.error-container h1 {
  color: #fff;
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.error-message {
  background: rgba(255, 107, 107, 0.2);
  border: 1px solid #ff6b6b;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  color: #fff;
}

.error-actions {
  margin-top: 30px;
}

/* ======================================== Registration Pages ======================================== */

.registration-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 30px;
  background: rgba(255,255,255,0.1);
  border-radius: 15px;
  border: 1px solid rgba(255,255,255,0.2);
}

.registration-container h1 {
  text-align: center;
  color: #fff;
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.registration-subtitle {
  text-align: center;
  color: #f0f0f0;
  font-size: 1.1rem;
  margin-bottom: 30px;
}

.registration-form {
  width: 100%;
  max-width: none;
}

.registration-form .form-group {
  margin-bottom: 25px;
}

.registration-form input,
.registration-form select {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 8px;
  background: rgba(255,255,255,0.1);
  color: #fff;
  font-size: 16px;
  transition: all 0.3s ease;
}

.registration-form input:focus,
.registration-form select:focus {
  outline: none;
  border-color: #1976d2;
  background: rgba(255,255,255,0.15);
  box-shadow: 0 0 10px rgba(25, 118, 210, 0.3);
}

.registration-form input::placeholder {
  color: rgba(255,255,255,0.6);
}

.registration-form label {
  display: block !important;
  color: #f0f0f0;
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 14px;
}

.login-link {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid rgba(255,255,255,0.2);
}

.login-link p {
  color: #f0f0f0;
  margin: 0;
}

.login-link a {
  color: #4fc3f7;
  text-decoration: none;
  font-weight: bold;
}

.login-link a:hover {
  color: #1565c0;
  text-decoration: underline;
}

/* Enhanced error styling for forms */
.registration-form .error {
  background: rgba(255, 107, 107, 0.2);
  border: 1px solid #ff6b6b;
  border-radius: 5px;
  padding: 8px 12px;
  margin-top: 5px;
  color: #ff6b6b;
  font-size: 14px;
  display: block;
}

.registration-form .error-message {
  background: rgba(255, 107, 107, 0.2);
  border: 1px solid #ff6b6b;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  color: #ff6b6b;
  text-align: center;
}

/* ======================================== Company Info Banner ======================================== */

.company-info-banner {
  background: rgba(236, 47, 0, 0.1);
  border: 1px solid rgba(25, 118, 210, 0.3);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 30px;
  text-align: center;
}

.company-info-banner h3 {
  color: #4fc3f7;
  margin-bottom: 10px;
  font-size: 1.5rem;
}

.company-info-banner p {
  color: #fff;
  margin: 8px 0;
  line-height: 1.5;
}

.company-info-banner .info-text {
  color: #f0f0f0;
  font-style: italic;
  margin-top: 15px;
  padding: 10px;
  background: rgba(255,255,255,0.05);
  border-radius: 5px;
}

/* ======================================== Services Pages ======================================== */

.services-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.services-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 30px 20px;
  background: rgba(255,255,255,0.1);
  border-radius: 15px;
}

.services-header h1 {
  color: #fff;
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.services-subtitle {
  color: #f0f0f0;
  font-size: 1.1rem;
  margin: 0;
}

.services-navigation {
  margin-bottom: 30px;
  text-align: center;
}

.nav-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.nav-link {
  padding: 10px 20px;
  background: rgba(255,255,255,0.1);
  color: #fff;
  text-decoration: none;
  border-radius: 25px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.nav-link:hover {
  background: rgba(25, 118, 210, 0.2);
  border-color: #1976d2;
  transform: translateY(-2px);
}

.nav-link.active {
  background: #1976d2;
  border-color: #1976d2;
  color: white;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.service-header h3 {
  margin: 0;
  flex: 1;
}

.count-badge, .new-badge, .category-badge {
  background: #1976d2;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
  margin-left: 10px;
}

.new-badge {
  background: #4CAF50;
}

.category-badge {
  background: rgba(255,255,255,0.2);
  color: #f0f0f0;
}

.service-info {
  margin-bottom: 20px;
}

.category-tag {
  background: rgba(25, 118, 210, 0.2);
  color: #4fc3f7;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: bold;
}

.price {
  color: #4CAF50;
  font-weight: bold;
  font-size: 1.1rem;
}

.popularity-indicator {
  margin-top: 15px;
  padding: 10px;
  background: rgba(255,255,255,0.05);
  border-radius: 8px;
}

.popularity-bar {
  width: 100%;
  height: 6px;
  background: rgba(255,255,255,0.2);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 5px;
}

.popularity-fill {
  height: 100%;
  background: linear-gradient(90deg, #1976d2, #42a5f5);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.popularity-text {
  color: #f0f0f0;
  font-size: 0.8rem;
}

.category-stats {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.stats-card {
  background: rgba(255,255,255,0.1);
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  border: 1px solid rgba(255,255,255,0.2);
}

.stats-card h4 {
  color: #4fc3f7;
  font-size: 2rem;
  margin: 0 0 5px 0;
}

.stats-card p {
  color: #f0f0f0;
  margin: 0;
  font-size: 0.9rem;
}

.services-footer {
  margin-top: 50px;
  padding: 30px;
  background: rgba(255,255,255,0.05);
  border-radius: 15px;
}

.services-footer h4 {
  color: #fff;
  text-align: center;
  margin-bottom: 20px;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.category-link {
  display: block;
  padding: 12px 15px;
  background: rgba(255,255,255,0.1);
  color: #fff;
  text-decoration: none;
  border-radius: 8px;
  text-align: center;
  border: 1px solid rgba(255,255,255,0.2);
  transition: all 0.3s ease;
}

.category-link:hover {
  background: rgba(25, 118, 210, 0.2);
  border-color: #1976d2;
  transform: translateY(-2px);
}

.restriction-note {
  color: #ff9800;
  font-style: italic;
  text-align: center;
  margin: 20px 0;
  padding: 15px;
  background: rgba(255, 152, 0, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 152, 0, 0.3);
}

/* ======================================== Service Detail Page ======================================== */

.service-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.service-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 30px;
  margin-bottom: 40px;
  padding: 30px;
  background: rgba(255,255,255,0.1);
  border-radius: 15px;
  border: 1px solid rgba(255,255,255,0.2);
}

.service-title-section h1 {
  color: #fff;
  font-size: 2.5rem;
  margin-bottom: 15px;
  line-height: 1.2;
}

.service-meta {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.category-badge, .price-badge, .new-badge {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: bold;
}

.category-badge {
  background: rgba(25, 118, 210, 0.2);
  color: #4fc3f7;
  border: 1px solid rgba(25, 118, 210, 0.3);
}

.price-badge {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
  font-size: 1.1rem;
}

.new-badge {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.service-company-preview {
  display: flex;
  gap: 15px;
  align-items: center;
  min-width: 300px;
}

.company-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #1976d2;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
}

.company-info-preview h3 {
  margin: 0 0 5px 0;
  color: #fff;
}

.company-link {
  color: #4fc3f7;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.3s ease;
}

.company-link:hover {
  color: #42a5f5;
  text-decoration: underline;
}

.company-specialization {
  color: #f0f0f0;
  margin: 0 0 8px 0;
  font-size: 0.9rem;
}

.company-rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-stars {
  font-size: 1rem;
}

.rating-text {
  color: #4CAF50;
  font-weight: bold;
  font-size: 0.9rem;
}

.no-rating {
  color: #999;
  font-size: 0.9rem;
}

.service-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

.service-main h2 {
  color: #fff;
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.service-description-section {
  margin-bottom: 40px;
  padding: 25px;
  background: rgba(255,255,255,0.05);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.1);
}

.description-content p {
  color: #f0f0f0;
  line-height: 1.6;
  font-size: 1.1rem;
  margin: 0;
}

.service-details-section {
  padding: 25px;
  background: rgba(255,255,255,0.05);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.1);
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.detail-card {
  display: flex;
  gap: 15px;
  padding: 20px;
  background: rgba(255,255,255,0.08);
  border-radius: 10px;
  border: 1px solid rgba(255,255,255,0.15);
  transition: transform 0.3s ease, background 0.3s ease;
}

.detail-card:hover {
  transform: translateY(-2px);
  background: rgba(255,255,255,0.12);
}

.detail-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(25, 118, 210, 0.2);
  border-radius: 8px;
  flex-shrink: 0;
}

.detail-content h4 {
  color: #fff;
  margin: 0 0 5px 0;
  font-size: 1rem;
}

.detail-content p {
  color: #f0f0f0;
  margin: 0;
  font-size: 0.95rem;
}

.detail-content small {
  color: #ccc;
  font-size: 0.8rem;
}

.price-highlight {
  color: #4CAF50 !important;
  font-weight: bold !important;
  font-size: 1.1rem !important;
}

.service-sidebar {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.company-card, .action-card {
  padding: 25px;
  background: rgba(255,255,255,0.1);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.2);
  overflow: hidden; /* Prevent content from overflowing the card */
}

.company-card h3, .action-card h3 {
  color: #fff;
  margin: 0 0 20px 0;
  font-size: 1.3rem;
}

.company-header {
  display: flex;
  gap: 15px;
  align-items: center;
  margin-bottom: 20px;
}

.company-avatar-large {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #1976d2;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  font-weight: bold;
  color: white;
}

.company-name-section h4 {
  margin: 0 0 5px 0;
  color: #fff;
}

.company-type {
  color: #f0f0f0;
  margin: 0;
  font-size: 0.9rem;
}

.company-stats {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: #ccc;
  font-size: 0.9rem;
}

.stat-value {
  color: #fff;
  font-weight: bold;
  font-size: 0.9rem;
}

.btn-outline {
  background: transparent;
  border: 2px solid #1976d2;
  color: #4fc3f7;
  padding: 12px 20px;
  border-radius: 8px;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  transition: all 0.3s ease;
  font-weight: bold;
}

.btn-outline:hover {
  background: #1976d2;
  color: white;
  transform: translateY(-2px);
}

.btn-large {
  padding: 15px 25px;
  font-size: 1.1rem;
  width: 100%;
  margin-bottom: 10px;
  box-sizing: border-box; /* Ensure padding is included in width calculation */
}

.action-note {
  color: #f0f0f0;
  font-size: 0.9rem;
  text-align: center;
  margin: 10px 0 0 0;
  line-height: 1.4;
}

/* Action card specific styling */
.action-card .btn-large {
  margin-bottom: 15px;
}

.action-card .owner-section,
.action-card .company-note {
  text-align: center;
}

.action-card .owner-badge {
  color: #4CAF50;
  font-weight: bold;
  margin-bottom: 15px;
}

.action-card .company-note p {
  color: #f0f0f0;
  margin-bottom: 15px;
}

.owner-section, .company-note, .login-section {
  text-align: center;
}

.owner-badge {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  padding: 10px 15px;
  border-radius: 8px;
  border: 1px solid rgba(76, 175, 80, 0.3);
  margin-bottom: 15px;
  font-weight: bold;
}

.login-note {
  color: #f0f0f0;
  margin-bottom: 15px;
}

.service-navigation {
  padding: 30px;
  background: rgba(255,255,255,0.05);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.1);
}

.service-navigation h4 {
  color: #fff;
  margin-bottom: 20px;
  text-align: center;
}

.nav-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.nav-btn {
  display: block;
  padding: 12px 20px;
  background: rgba(255,255,255,0.1);
  color: #fff;
  text-decoration: none;
  border-radius: 8px;
  text-align: center;
  border: 1px solid rgba(255,255,255,0.2);
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: rgba(25, 118, 210, 0.2);
  border-color: #1976d2;
  transform: translateY(-2px);
}

/* Related Services Sections */
.related-services-section {
  margin-bottom: 40px;
  padding: 30px;
  background: rgba(255,255,255,0.05);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.1);
}

.related-services-section h2 {
  color: #fff;
  margin-bottom: 25px;
  font-size: 1.6rem;
  text-align: center;
}

.related-services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.related-service-card {
  padding: 20px;
  background: rgba(255,255,255,0.08);
  border-radius: 10px;
  border: 1px solid rgba(255,255,255,0.15);
  transition: transform 0.3s ease, background 0.3s ease;
}

.related-service-card:hover {
  transform: translateY(-3px);
  background: rgba(255,255,255,0.12);
}

.related-service-header {
  margin-bottom: 12px;
}

.related-service-header h4 {
  margin: 0 0 5px 0;
  color: #fff;
  font-size: 1.1rem;
}

.related-service-header h4 a {
  color: #fff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.related-service-header h4 a:hover {
  color: #4fc3f7;
}

.related-category {
  background: rgba(25, 118, 210, 0.2);
  color: #4fc3f7;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.related-company {
  color: #f0f0f0;
  font-size: 0.9rem;
}

.related-company a {
  color: #4fc3f7;
  text-decoration: none;
  font-weight: bold;
}

.related-company a:hover {
  text-decoration: underline;
}

.related-description {
  color: #f0f0f0;
  margin: 12px 0;
  line-height: 1.4;
  font-size: 0.95rem;
}

.related-service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.related-price {
  color: #4CAF50;
  font-weight: bold;
  font-size: 1rem;
}

.btn-small {
  padding: 8px 16px;
  font-size: 0.9rem;
  background: #1976d2;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.btn-small:hover {
  background: #42a5f5;
  transform: translateY(-1px);
}

.view-all-company-services, .view-all-category-services {
  text-align: center;
  margin-top: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .service-detail-header {
    flex-direction: column;
    gap: 20px;
  }

  .service-company-preview {
    min-width: auto;
    width: 100%;
  }

  .service-content {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .nav-links {
    grid-template-columns: 1fr;
  }

  .related-services-grid {
    grid-template-columns: 1fr;
  }

  .related-service-footer {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}

/* ======================================== Customer Profile ======================================== */

.customer-profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.customer-profile-header {
  display: flex;
  gap: 25px;
  align-items: center;
  margin-bottom: 40px;
  padding: 30px;
  background: rgba(255,255,255,0.1);
  border-radius: 15px;
  border: 1px solid rgba(255,255,255,0.2);
}

.customer-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #1976d2;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  color: white;
  flex-shrink: 0;
}

.customer-info h1 {
  color: #fff;
  margin: 0 0 5px 0;
  font-size: 2.2rem;
}

.customer-type {
  color: #f0f0f0;
  margin: 0 0 20px 0;
  font-size: 1.1rem;
}

.customer-stats {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  color: #4fc3f7;
  font-size: 1.8rem;
  font-weight: bold;
  line-height: 1;
}

.stat-label {
  color: #f0f0f0;
  font-size: 0.9rem;
  margin-top: 5px;
}

.customer-account-info {
  margin-bottom: 40px;
  padding: 30px;
  background: rgba(255,255,255,0.05);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.1);
}

.customer-account-info h2 {
  color: #fff;
  margin-bottom: 25px;
  font-size: 1.8rem;
}

.account-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.info-card {
  display: flex;
  gap: 15px;
  padding: 20px;
  background: rgba(255,255,255,0.08);
  border-radius: 10px;
  border: 1px solid rgba(255,255,255,0.15);
  transition: transform 0.3s ease, background 0.3s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  background: rgba(255,255,255,0.12);
}

.info-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(25, 118, 210, 0.2);
  border-radius: 8px;
  flex-shrink: 0;
}

.info-content h4 {
  color: #fff;
  margin: 0 0 5px 0;
  font-size: 1rem;
}

.info-content p {
  color: #f0f0f0;
  margin: 0;
  font-size: 0.95rem;
}

.info-content small {
  color: #ccc;
  font-size: 0.8rem;
}

.status-active {
  color: #4CAF50;
  font-weight: bold;
}

.status-inactive {
  color: #f44336;
  font-weight: bold;
}

.service-requests-section {
  margin-bottom: 40px;
}

.requests-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 25px;
  background: rgba(255,255,255,0.05);
  border-radius: 12px;
}

.requests-header h2 {
  color: #fff;
  margin-bottom: 10px;
  font-size: 2rem;
}

.requests-subtitle {
  color: #f0f0f0;
  margin: 0;
  font-size: 1.1rem;
}

.requests-summary {
  margin-bottom: 30px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.summary-card {
  text-align: center;
  padding: 20px;
  background: rgba(255,255,255,0.1);
  border-radius: 10px;
  border: 1px solid rgba(255,255,255,0.2);
}

.summary-card h4 {
  color: #4fc3f7;
  font-size: 1.8rem;
  margin: 0 0 5px 0;
}

.summary-card p {
  color: #f0f0f0;
  margin: 0;
  font-size: 0.9rem;
}

.requests-list {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.request-card {
  padding: 25px;
  background: rgba(255,255,255,0.08);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.15);
  transition: transform 0.3s ease, background 0.3s ease;
}

.request-card:hover {
  transform: translateY(-3px);
  background: rgba(255,255,255,0.12);
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.request-title h3 {
  margin: 0 0 8px 0;
  color: #fff;
  font-size: 1.3rem;
}

.request-title h3 a {
  color: #fff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.request-title h3 a:hover {
  color: #4fc3f7;
}

.request-category {
  background: rgba(25, 118, 210, 0.2);
  color: #4fc3f7;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
}

.request-cost {
  text-align: right;
  flex-shrink: 0;
}

.cost-amount {
  display: block;
  color: #4CAF50;
  font-size: 1.5rem;
  font-weight: bold;
  line-height: 1;
}

.cost-breakdown {
  color: #ccc;
  font-size: 0.8rem;
  margin-top: 3px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.detail-icon {
  font-size: 1.2rem;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(236, 47, 0, 0.15);
  border-radius: 6px;
  flex-shrink: 0;
}

.detail-content label {
  display: block;
  color: #ccc;
  font-size: 0.8rem;
  margin-bottom: 3px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-content p {
  color: #fff;
  margin: 0;
  font-size: 0.95rem;
  font-weight: 500;
}

.detail-content small {
  color: #ccc;
  font-size: 0.8rem;
}

.company-link {
  color: #4fc3f7;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.3s ease;
}

.company-link:hover {
  color: #42a5f5;
  text-decoration: underline;
}

.cost-highlight {
  color: #4CAF50 !important;
  font-weight: bold !important;
  font-size: 1.1rem !important;
}

.request-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.no-requests {
  text-align: center;
  padding: 60px 30px;
  background: rgba(255,255,255,0.05);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.1);
}

.no-requests-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.no-requests h3 {
  color: #fff;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.no-requests p {
  color: #f0f0f0;
  margin-bottom: 30px;
  font-size: 1.1rem;
}

.no-requests-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.profile-actions {
  text-align: center;
  padding: 30px;
  background: rgba(255,255,255,0.05);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.1);
}

.action-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
  .customer-profile-header {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .customer-stats {
    justify-content: center;
  }

  .account-info-grid {
    grid-template-columns: 1fr;
  }

  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .request-header {
    flex-direction: column;
    gap: 15px;
  }

  .request-cost {
    text-align: left;
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }

  .request-actions {
    justify-content: center;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
}

/* ======================================== Company Profile ======================================== */

.company-profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.company-profile-header {
  display: flex;
  gap: 25px;
  align-items: center;
  margin-bottom: 40px;
  padding: 30px;
  background: rgba(255,255,255,0.1);
  border-radius: 15px;
  border: 1px solid rgba(255,255,255,0.2);
}

.company-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  color: white;
  flex-shrink: 0;
}

.company-initial {
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.company-info h1 {
  color: #fff;
  margin: 0 0 5px 0;
  font-size: 2.2rem;
}

.company-type {
  color: #f0f0f0;
  margin: 0 0 20px 0;
  font-size: 1.1rem;
}

.company-stats {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.company-account-info {
  margin-bottom: 40px;
}

.company-account-info h2 {
  color: #fff;
  margin-bottom: 25px;
  font-size: 1.8rem;
  text-align: center;
}

.specialization-highlight {
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9rem;
}

.company-services-section {
  margin-bottom: 40px;
}

.services-header {
  text-align: center;
  margin-bottom: 30px;
}

.services-header h2 {
  color: #fff;
  margin-bottom: 10px;
  font-size: 1.8rem;
}

.services-subtitle {
  color: #f0f0f0;
  font-size: 1.1rem;
  margin: 0;
}

.services-summary {
  margin-bottom: 30px;
}

.services-list {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.service-card {
  background: rgba(255,255,255,0.1);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(255,255,255,0.2);
  transition: all 0.3s ease;
}

.service-card:hover {
  background: rgba(255,255,255,0.15);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.service-title h3 {
  margin: 0 0 8px 0;
  font-size: 1.4rem;
}

.service-title h3 a {
  color: #4fc3f7;
  text-decoration: none;
  transition: color 0.3s ease;
}

.service-title h3 a:hover {
  color: #29b6f6;
  text-decoration: underline;
}

.service-category {
  background: rgba(79, 195, 247, 0.2);
  color: #4fc3f7;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: bold;
}

.service-price {
  text-align: right;
}

.price-amount {
  font-size: 1.8rem;
  font-weight: bold;
  color: #4CAF50;
}

.price-unit {
  display: block;
  color: #ccc;
  font-size: 0.9rem;
  margin-top: 2px;
}

.service-description {
  margin-bottom: 20px;
}

.service-description p {
  color: #f0f0f0;
  line-height: 1.6;
  margin: 0;
}

.service-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.meta-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.meta-icon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.meta-content label {
  display: block;
  color: #ccc;
  font-size: 0.85rem;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.meta-content p {
  color: #fff;
  margin: 0;
  font-weight: 500;
}

.meta-content small {
  color: #bbb;
  font-size: 0.8rem;
}

.price-highlight {
  color: #4CAF50 !important;
  font-weight: bold;
}

.service-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.no-services {
  text-align: center;
  padding: 60px 20px;
  background: rgba(255,255,255,0.05);
  border-radius: 15px;
  border: 2px dashed rgba(255,255,255,0.2);
}

.no-services-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.no-services h3 {
  color: #fff;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.no-services p {
  color: #ccc;
  margin-bottom: 30px;
  font-size: 1.1rem;
}

.no-services-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design for Company Profile */
@media (max-width: 768px) {
  .company-profile-header {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .company-stats {
    justify-content: center;
  }

  .service-header {
    flex-direction: column;
    gap: 15px;
  }

  .service-price {
    text-align: left;
  }

  .service-meta {
    grid-template-columns: 1fr;
  }

  .service-actions {
    justify-content: center;
  }

  .no-services-actions {
    flex-direction: column;
    align-items: center;
  }
}

/* ======================================== Service Request Page ======================================== */

.request-service-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.request-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 25px;
  background: rgba(255,255,255,0.1);
  border-radius: 12px;
}

.request-header h1 {
  color: #fff;
  margin-bottom: 10px;
  font-size: 2.2rem;
}

.request-subtitle {
  color: #f0f0f0;
  margin: 0;
  font-size: 1.1rem;
}

.service-summary-card {
  margin-bottom: 30px;
  padding: 25px;
  background: rgba(255,255,255,0.08);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.15);
}

.service-summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.service-summary-header h2 {
  color: #fff;
  margin: 0;
  font-size: 1.5rem;
}

.service-category {
  background: rgba(25, 118, 210, 0.2);
  color: #4fc3f7;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: bold;
}

.service-summary-content {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 25px;
  align-items: center;
}

.service-main-info h3 {
  color: #fff;
  margin: 0 0 10px 0;
  font-size: 1.3rem;
}

.service-description {
  color: #f0f0f0;
  margin: 0;
  line-height: 1.5;
}

.service-provider-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.provider-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #1976d2;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
}

.provider-details h4 {
  margin: 0 0 3px 0;
  color: #fff;
  font-size: 1rem;
}

.provider-type {
  color: #f0f0f0;
  margin: 0 0 5px 0;
  font-size: 0.85rem;
}

.provider-rating {
  display: flex;
  align-items: center;
  gap: 5px;
}

.rating-stars {
  font-size: 0.9rem;
}

.rating-text {
  color: #4CAF50;
  font-size: 0.8rem;
  font-weight: bold;
}

.no-rating {
  color: #999;
  font-size: 0.8rem;
}

.service-pricing {
  text-align: center;
  padding: 15px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.price-display {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.price-amount {
  color: #4CAF50;
  font-size: 1.8rem;
  font-weight: bold;
  line-height: 1;
}

.price-unit {
  color: #f0f0f0;
  font-size: 0.9rem;
  margin-top: 3px;
}

.request-form-card {
  margin-bottom: 30px;
  padding: 30px;
  background: rgba(255,255,255,0.08);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.15);
}

.form-header {
  margin-bottom: 25px;
  text-align: center;
}

.form-header h2 {
  color: #fff;
  margin-bottom: 8px;
  font-size: 1.6rem;
}

.form-header p {
  color: #f0f0f0;
  margin: 0;
}

.form-section {
  margin-bottom: 25px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 1rem;
}

.label-icon {
  font-size: 1.1rem;
}

.form-group input, .form-group textarea {
  width: 100%;
  padding: 12px 15px;
  background: rgba(255,255,255,0.1);
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 8px;
  color: #fff;
  font-size: 1rem;
  transition: border-color 0.3s ease, background 0.3s ease;
}

.form-group input:focus, .form-group textarea:focus {
  outline: none;
  border-color: #1976d2;
  background: rgba(255,255,255,0.15);
}

.form-group input::placeholder, .form-group textarea::placeholder {
  color: #ccc;
}

.form-help {
  display: block;
  color: #ccc;
  font-size: 0.85rem;
  margin-top: 5px;
}

.form-error {
  color: #f44336;
  font-size: 0.9rem;
  margin-top: 5px;
  padding: 8px 12px;
  background: rgba(244, 67, 54, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.cost-calculator {
  margin: 25px 0;
  padding: 20px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.cost-calculator h3 {
  color: #4CAF50;
  margin: 0 0 15px 0;
  font-size: 1.2rem;
}

.cost-breakdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.cost-item:not(:last-child) {
  border-bottom: 1px solid rgba(76, 175, 80, 0.2);
}

.cost-total {
  font-weight: bold;
  font-size: 1.1rem;
  padding-top: 12px;
  border-top: 2px solid rgba(76, 175, 80, 0.4);
}

.cost-label {
  color: #f0f0f0;
}

.cost-value {
  color: #4CAF50;
  font-weight: bold;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
  flex-wrap: wrap;
  text-align: center;
}

/* Access Denied Section Styling */
.access-denied-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  text-align: center;
  display: block;
}

.request-form-card .form-header {
  text-align: center;
}

.request-form-card .form-header h2 {
  color: #fff;
  margin: 15px 0;
  font-size: 1.8rem;
}

.request-form-card .form-header p {
  color: #f0f0f0;
  margin: 0 0 20px 0;
  font-size: 1.1rem;
  line-height: 1.5;
}

/* Company Information Styling */
.company-info-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.company-profile {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: rgba(255,255,255,0.05);
  border-radius: 10px;
  border: 1px solid rgba(255,255,255,0.1);
}

.company-details {
  flex: 1;
}

.company-details h3 {
  color: #4fc3f7;
  margin: 0 0 8px 0;
  font-size: 1.4rem;
}

.company-specialization {
  color: #f0f0f0;
  margin: 0 0 10px 0;
  font-size: 1rem;
}

.company-rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.company-badge {
  display: flex;
  align-items: center;
}

.company-permissions {
  padding: 0;
}

.permission-info {
  display: flex;
  gap: 15px;
  align-items: flex-start;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid rgba(255,255,255,0.1);
}

.permission-info.all-categories {
  background: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
}

.permission-info.limited-category {
  background: rgba(255, 152, 0, 0.1);
  border-color: rgba(255, 152, 0, 0.3);
}

.permission-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.permission-text h4 {
  color: #fff;
  margin: 0 0 8px 0;
  font-size: 1.1rem;
}

.permission-text p {
  color: #f0f0f0;
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.4;
}

/* Removed duplicate .btn-large definition - consolidated above */

.request-info {
  margin-bottom: 30px;
  text-align: center;
}

.info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  justify-items: center;
  max-width: 1000px;
  margin: 0 auto;
}

.info-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 15px;
  padding: 25px 20px;
  background: rgba(255,255,255,0.05);
  border-radius: 10px;
  border: 1px solid rgba(255,255,255,0.1);
  width: 100%;
  max-width: 300px;
  transition: transform 0.3s ease, background 0.3s ease;
}

.info-card:hover {
  transform: translateY(-5px);
  background: rgba(255,255,255,0.08);
}

.info-icon {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(25, 118, 210, 0.2);
  border-radius: 50%;
  flex-shrink: 0;
  margin-bottom: 5px;
}

.info-content {
  text-align: center;
  width: 100%;
}

.info-content h4 {
  color: #4fc3f7;
  margin: 0 0 12px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.info-content p {
  color: #f0f0f0;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

.info-content a {
  color: #4fc3f7;
  text-decoration: none;
  font-weight: bold;
}

.info-content a:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .service-summary-content {
    grid-template-columns: 1fr;
    gap: 20px;
    text-align: center;
  }

  .service-provider-info {
    justify-content: center;
  }

  .form-actions {
    flex-direction: column;
    align-items: center;
  }

  .info-cards {
    grid-template-columns: 1fr;
  }

  .cost-item {
    flex-direction: column;
    gap: 5px;
    text-align: center;
  }

  .company-profile {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .company-badge {
    justify-content: center;
  }
}

/* ======================================== Service Requests Section ======================================== */

.service-requests-section {
  margin-bottom: 40px;
}

.requests-header {
  text-align: center;
  margin-bottom: 30px;
}

.requests-header h2 {
  color: #4fc3f7;
  font-size: 2.2rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.requests-subtitle {
  color: #b3e5fc;
  font-size: 1.1rem;
  margin: 0;
}

.requests-summary {
  margin-bottom: 30px;
}

.requests-list {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.request-card {
  background: rgba(255,255,255,0.1);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(255,255,255,0.2);
  transition: all 0.3s ease;
}

.request-card:hover {
  background: rgba(255,255,255,0.15);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.request-customer {
  display: flex;
  align-items: center;
  gap: 15px;
}

.customer-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #1e90ff, #4fc3f7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 1.2rem;
}

.customer-info h4 {
  color: #4fc3f7;
  margin: 0 0 5px 0;
  font-size: 1.2rem;
}

.customer-email {
  color: #b3e5fc;
  margin: 0;
  font-size: 0.9rem;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
}

.status-pending {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.request-details {
  margin-bottom: 20px;
}

.request-service h5 {
  color: #4fc3f7;
  margin: 0 0 5px 0;
  font-size: 1.3rem;
}

.service-category {
  color: #b3e5fc;
  margin: 0 0 15px 0;
  font-size: 0.9rem;
  font-weight: 500;
}

.request-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.request-info-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-label {
  color: #b3e5fc;
  font-size: 0.9rem;
  min-width: 100px;
}

.info-value {
  color: #ffffff;
  font-weight: 500;
}

.request-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn-sm {
  padding: 8px 16px;
  font-size: 0.85rem;
}

.no-requests {
  text-align: center;
  padding: 60px 20px;
  background: rgba(255,255,255,0.05);
  border-radius: 15px;
  border: 2px dashed rgba(255,255,255,0.2);
}

.no-requests-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.no-requests h3 {
  color: #4fc3f7;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.no-requests p {
  color: #b3e5fc;
  margin-bottom: 25px;
  font-size: 1.1rem;
}

.no-requests-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design for Service Requests */
@media (max-width: 768px) {
  .request-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .request-info-grid {
    grid-template-columns: 1fr;
  }

  .request-actions {
    justify-content: center;
  }

  .no-requests-actions {
    flex-direction: column;
    align-items: center;
  }
}

/* ============================================================================================== */
